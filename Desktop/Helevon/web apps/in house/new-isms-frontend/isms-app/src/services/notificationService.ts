import { secureInvoke } from '../utils/apiInterceptor';
import { Notification, Product } from '../types';

export const notificationService = {
  // Get notifications for current user
  getNotifications: async (unreadOnly: boolean = false): Promise<Notification[]> => {
    return await secureInvoke('get_notifications', { 
      userId: null, // null means current user
      unreadOnly 
    });
  },

  // Mark notification as read
  markAsRead: async (notificationId: number): Promise<void> => {
    return await secureInvoke('mark_notification_read', { notificationId });
  },

  // Get products expiring soon
  getExpiringProducts: async (daysAhead: number = 7): Promise<Product[]> => {
    return await secureInvoke('get_expiring_products', { daysAhead });
  },

  // Manually trigger alert checking (for admins)
  checkAlerts: async (): Promise<void> => {
    return await secureInvoke('check_alerts');
  },

  // Create a notification (admin only)
  createNotification: async (data: {
    userId?: number;
    title: string;
    message: string;
    type: string;
    priority: string;
    productId?: number;
  }): Promise<number> => {
    return await secureInvoke('create_notification', {
      userId: data.userId,
      title: data.title,
      message: data.message,
      notificationType: data.type,
      priority: data.priority,
      productId: data.productId,
    });
  },
};
