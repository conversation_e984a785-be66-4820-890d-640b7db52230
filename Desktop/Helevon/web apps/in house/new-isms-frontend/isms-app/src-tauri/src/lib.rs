use tauri::Manager;
use std::sync::Arc;
use tokio::sync::Mutex;

mod database;
mod auth;
mod models;

use database::Database;

// Application state
pub struct AppState {
    pub db: Arc<Mutex<Database>>,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

// Database initialization command
#[tauri::command]
async fn init_database(app_handle: tauri::AppHandle) -> Result<String, String> {
    let app_data_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data directory: {}", e))?;

    // Ensure the directory exists
    std::fs::create_dir_all(&app_data_dir)
        .map_err(|e| format!("Failed to create app data directory: {}", e))?;

    let db_path = app_data_dir.join("isms.db");
    let db_url = format!("sqlite:{}", db_path.to_string_lossy());

    let mut database = Database::new(&db_url).await
        .map_err(|e| format!("Failed to initialize database: {}", e))?;

    database.migrate().await
        .map_err(|e| format!("Failed to run migrations: {}", e))?;

    // Store database in app state
    let state = app_handle.state::<AppState>();
    *state.db.lock().await = database;

    Ok("Database initialized successfully".to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_sql::Builder::default().build())
        .plugin(tauri_plugin_store::Builder::default().build())
        .setup(|app| {
            // Initialize app state
            let app_state = AppState {
                db: Arc::new(Mutex::new(Database::default())),
            };
            app.manage(app_state);
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            greet,
            init_database,
            auth::login,
            auth::logout,
            auth::get_current_user,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
