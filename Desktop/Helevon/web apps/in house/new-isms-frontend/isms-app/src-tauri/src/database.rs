use sqlx::{SqlitePool, Row};
use anyhow::Result;
use crate::models::*;

#[derive(Clone)]
pub struct Database {
    pub pool: Option<SqlitePool>,
}

impl Default for Database {
    fn default() -> Self {
        Self { pool: None }
    }
}

impl Database {
    pub async fn new(database_url: &str) -> Result<Self> {
        let pool = SqlitePool::connect(database_url).await?;
        Ok(Self { pool: Some(pool) })
    }

    pub fn create_placeholder() -> Self {
        Self { pool: None }
    }

    pub async fn migrate(&mut self) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;
        
        // Create tables
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                password_hash TEXT NOT NULL,
                last_login DATETIME,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS role_permissions (
                role_id INTEGER NOT NULL,
                permission_id INTEGER NOT NULL,
                PRIMARY KEY (role_id, permission_id),
                FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE,
                FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                sku TEXT UNIQUE NOT NULL,
                category TEXT NOT NULL,
                price REAL NOT NULL,
                cost REAL NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 0,
                reorder_level INTEGER NOT NULL DEFAULT 0,
                supplier_id INTEGER,
                last_synced_at DATETIME,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_name TEXT,
                email TEXT,
                phone TEXT,
                address TEXT,
                last_synced_at DATETIME,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_name TEXT,
                payment_method TEXT NOT NULL,
                total_amount REAL NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                order_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                last_synced_at DATETIME,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                price_at_sale REAL NOT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS inventory_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                quantity_change INTEGER NOT NULL,
                movement_type TEXT NOT NULL,
                notes TEXT,
                timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                last_synced_at DATETIME,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
            "#,
        )
        .execute(pool)
        .await?;

        // Insert default roles and permissions
        self.seed_default_data().await?;

        Ok(())
    }

    pub async fn seed_default_data(&self) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;
        
        // Insert default roles
        let roles = vec![
            ("admin", "System Administrator"),
            ("manager", "Store Manager"),
            ("cashier", "Cashier"),
            ("inventory", "Inventory Clerk"),
        ];

        for (name, description) in roles {
            sqlx::query(
                "INSERT OR IGNORE INTO roles (name, description) VALUES (?, ?)"
            )
            .bind(name)
            .bind(description)
            .execute(pool)
            .await?;
        }

        // Insert default permissions
        let permissions = vec![
            ("user_management", "Manage users and roles"),
            ("inventory_management", "Manage inventory and products"),
            ("sales_management", "Process sales and orders"),
            ("reporting", "View reports and analytics"),
            ("system_settings", "Manage system settings"),
        ];

        for (name, description) in permissions {
            sqlx::query(
                "INSERT OR IGNORE INTO permissions (name, description) VALUES (?, ?)"
            )
            .bind(name)
            .bind(description)
            .execute(pool)
            .await?;
        }

        // Assign permissions to roles
        let role_permissions = vec![
            ("admin", vec!["user_management", "inventory_management", "sales_management", "reporting", "system_settings"]),
            ("manager", vec!["inventory_management", "sales_management", "reporting"]),
            ("cashier", vec!["sales_management"]),
            ("inventory", vec!["inventory_management"]),
        ];

        for (role_name, perms) in role_permissions {
            let role_id: i64 = sqlx::query("SELECT id FROM roles WHERE name = ?")
                .bind(role_name)
                .fetch_one(pool)
                .await?
                .get(0);

            for perm_name in perms {
                let perm_id: i64 = sqlx::query("SELECT id FROM permissions WHERE name = ?")
                    .bind(perm_name)
                    .fetch_one(pool)
                    .await?
                    .get(0);

                sqlx::query(
                    "INSERT OR IGNORE INTO role_permissions (role_id, permission_id) VALUES (?, ?)"
                )
                .bind(role_id)
                .bind(perm_id)
                .execute(pool)
                .await?;
            }
        }

        // Create default admin user if it doesn't exist
        println!("Checking if admin user exists...");
        let admin_exists = sqlx::query("SELECT COUNT(*) as count FROM users WHERE email = '<EMAIL>'")
            .fetch_one(pool)
            .await?
            .get::<i64, _>(0) > 0;

        if !admin_exists {
            println!("Creating default admin user...");
            let password_hash = bcrypt::hash("admin123", bcrypt::DEFAULT_COST)?;
            println!("Password hash created: {}", password_hash);
            sqlx::query(
                "INSERT INTO users (email, full_name, role, password_hash) VALUES (?, ?, ?, ?)"
            )
            .bind("<EMAIL>")
            .bind("System Administrator")
            .bind("admin")
            .bind(password_hash)
            .execute(pool)
            .await?;
            println!("Admin user created successfully");
        } else {
            println!("Admin user already exists");
        }

        Ok(())
    }

    pub async fn get_user_by_email(&self, email: &str) -> Result<Option<User>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;
        
        let user = sqlx::query_as::<_, User>(
            "SELECT * FROM users WHERE email = ?"
        )
        .bind(email)
        .fetch_optional(pool)
        .await?;

        Ok(user)
    }

    pub async fn get_user_permissions(&self, user_id: i64) -> Result<Vec<String>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;
        
        let permissions = sqlx::query(
            r#"
            SELECT p.name 
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN roles r ON rp.role_id = r.id
            JOIN users u ON u.role = r.name
            WHERE u.id = ?
            "#
        )
        .bind(user_id)
        .fetch_all(pool)
        .await?;

        Ok(permissions.into_iter().map(|row| row.get::<String, _>(0)).collect())
    }
}
