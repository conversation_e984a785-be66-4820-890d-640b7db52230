use sqlx::{SqlitePool, Row};
use anyhow::Result;
use crate::models::*;

#[derive(Clone)]
pub struct Database {
    pub pool: Option<SqlitePool>,
}

impl Default for Database {
    fn default() -> Self {
        Self { pool: None }
    }
}

impl Database {
    pub async fn new(database_url: &str) -> Result<Self> {
        let pool = SqlitePool::connect(database_url).await?;
        Ok(Self { pool: Some(pool) })
    }

    pub fn create_placeholder() -> Self {
        Self { pool: None }
    }

    pub fn is_placeholder(&self) -> bool {
        self.pool.is_none()
    }

    pub async fn migrate(&mut self) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;
        
        // Create tables
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                password_hash TEXT NOT NULL,
                last_login DATETIME,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS role_permissions (
                role_id INTEGER NOT NULL,
                permission_id INTEGER NOT NULL,
                PRIMARY KEY (role_id, permission_id),
                FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE,
                FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                sku TEXT UNIQUE NOT NULL,
                category TEXT NOT NULL,
                price REAL NOT NULL,
                cost REAL NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 0,
                reorder_level INTEGER NOT NULL DEFAULT 0,
                expiry_date DATE,
                supplier_id INTEGER,
                last_synced_at DATETIME,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_name TEXT,
                email TEXT,
                phone TEXT,
                address TEXT,
                last_synced_at DATETIME,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_name TEXT,
                payment_method TEXT NOT NULL,
                total_amount REAL NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                order_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                last_synced_at DATETIME,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                price_at_sale REAL NOT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS inventory_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                quantity_change INTEGER NOT NULL,
                movement_type TEXT NOT NULL,
                notes TEXT,
                timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                last_synced_at DATETIME,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
            "#,
        )
        .execute(pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                type TEXT NOT NULL,
                priority TEXT NOT NULL DEFAULT 'medium',
                is_read BOOLEAN NOT NULL DEFAULT FALSE,
                product_id INTEGER,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
            "#,
        )
        .execute(pool)
        .await?;

        // Insert default roles and permissions
        self.seed_default_data().await?;

        Ok(())
    }

    pub async fn seed_default_data(&self) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;
        
        // Insert default roles
        let roles = vec![
            ("admin", "System Administrator"),
            ("manager", "Store Manager"),
            ("cashier", "Cashier"),
            ("inventory", "Inventory Clerk"),
        ];

        for (name, description) in roles {
            sqlx::query(
                "INSERT OR IGNORE INTO roles (name, description) VALUES (?, ?)"
            )
            .bind(name)
            .bind(description)
            .execute(pool)
            .await?;
        }

        // Insert default permissions
        let permissions = vec![
            ("user_management", "Manage users and roles"),
            ("inventory_management", "Manage inventory and products"),
            ("sales_management", "Process sales and orders"),
            ("reporting", "View reports and analytics"),
            ("system_settings", "Manage system settings"),
        ];

        for (name, description) in permissions {
            sqlx::query(
                "INSERT OR IGNORE INTO permissions (name, description) VALUES (?, ?)"
            )
            .bind(name)
            .bind(description)
            .execute(pool)
            .await?;
        }

        // Assign permissions to roles
        let role_permissions = vec![
            ("admin", vec!["user_management", "inventory_management", "sales_management", "reporting", "system_settings"]),
            ("manager", vec!["inventory_management", "sales_management", "reporting"]),
            ("cashier", vec!["sales_management"]),
            ("inventory", vec!["inventory_management"]),
        ];

        for (role_name, perms) in role_permissions {
            let role_id: i64 = sqlx::query("SELECT id FROM roles WHERE name = ?")
                .bind(role_name)
                .fetch_one(pool)
                .await?
                .get(0);

            for perm_name in perms {
                let perm_id: i64 = sqlx::query("SELECT id FROM permissions WHERE name = ?")
                    .bind(perm_name)
                    .fetch_one(pool)
                    .await?
                    .get(0);

                sqlx::query(
                    "INSERT OR IGNORE INTO role_permissions (role_id, permission_id) VALUES (?, ?)"
                )
                .bind(role_id)
                .bind(perm_id)
                .execute(pool)
                .await?;
            }
        }

        // Create default admin user if it doesn't exist
        println!("Checking if admin user exists...");
        let admin_exists = sqlx::query("SELECT COUNT(*) as count FROM users WHERE email = '<EMAIL>'")
            .fetch_one(pool)
            .await?
            .get::<i64, _>(0) > 0;

        if !admin_exists {
            println!("Creating default admin user...");
            let password_hash = bcrypt::hash("admin123", bcrypt::DEFAULT_COST)?;
            println!("Password hash created: {}", password_hash);
            sqlx::query(
                "INSERT INTO users (email, full_name, role, password_hash) VALUES (?, ?, ?, ?)"
            )
            .bind("<EMAIL>")
            .bind("System Administrator")
            .bind("admin")
            .bind(password_hash)
            .execute(pool)
            .await?;
            println!("Admin user created successfully");
        } else {
            println!("Admin user already exists");
        }

        // Add sample data for development
        self.seed_sample_data().await?;

        Ok(())
    }

    async fn seed_sample_data(&self) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        // Check if sample data already exists
        let supplier_count = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM suppliers")
            .fetch_one(pool)
            .await?;

        if supplier_count > 0 {
            println!("Sample data already exists");
            return Ok(());
        }

        println!("Seeding sample data...");

        // Add sample suppliers
        let supplier_ids = vec![
            sqlx::query("INSERT INTO suppliers (name, contact_name, email, phone, address) VALUES (?, ?, ?, ?, ?)")
                .bind("ABC Wholesale")
                .bind("John Smith")
                .bind("<EMAIL>")
                .bind("******-0123")
                .bind("123 Business St, City, State 12345")
                .execute(pool)
                .await?
                .last_insert_rowid(),

            sqlx::query("INSERT INTO suppliers (name, contact_name, email, phone, address) VALUES (?, ?, ?, ?, ?)")
                .bind("Fresh Foods Inc")
                .bind("Sarah Johnson")
                .bind("<EMAIL>")
                .bind("******-0456")
                .bind("456 Market Ave, City, State 12345")
                .execute(pool)
                .await?
                .last_insert_rowid(),
        ];

        // Add sample products
        let sample_products = vec![
            ("Coca Cola 500ml", "Refreshing cola drink", "CC500", "Beverages", 2.50, 1.80, 50, 10, Some("2024-12-31"), Some(supplier_ids[0])),
            ("Bread Loaf", "Fresh white bread", "BL001", "Bakery", 3.99, 2.50, 25, 5, Some("2024-07-15"), Some(supplier_ids[1])),
            ("Milk 1L", "Fresh whole milk", "MK1L", "Dairy", 4.50, 3.20, 30, 8, Some("2024-07-10"), Some(supplier_ids[1])),
            ("Chips BBQ", "Barbecue flavored chips", "CH001", "Snacks", 2.99, 1.50, 40, 10, Some("2025-03-15"), Some(supplier_ids[0])),
            ("Apple Juice 1L", "100% pure apple juice", "AJ1L", "Beverages", 5.99, 4.20, 20, 5, Some("2024-09-30"), Some(supplier_ids[1])),
        ];

        for (name, description, sku, category, price, cost, quantity, reorder_level, expiry_date, supplier_id) in sample_products {
            sqlx::query("INSERT INTO products (name, description, sku, category, price, cost, quantity, reorder_level, expiry_date, supplier_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
                .bind(name)
                .bind(description)
                .bind(sku)
                .bind(category)
                .bind(price)
                .bind(cost)
                .bind(quantity)
                .bind(reorder_level)
                .bind(expiry_date)
                .bind(supplier_id)
                .execute(pool)
                .await?;
        }

        println!("Sample data seeded successfully");
        Ok(())
    }

    pub async fn get_user_by_email(&self, email: &str) -> Result<Option<User>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;
        
        let user = sqlx::query_as::<_, User>(
            "SELECT * FROM users WHERE email = ?"
        )
        .bind(email)
        .fetch_optional(pool)
        .await?;

        Ok(user)
    }

    pub async fn get_user_permissions(&self, user_id: i64) -> Result<Vec<String>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;
        
        let permissions = sqlx::query(
            r#"
            SELECT p.name 
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN roles r ON rp.role_id = r.id
            JOIN users u ON u.role = r.name
            WHERE u.id = ?
            "#
        )
        .bind(user_id)
        .fetch_all(pool)
        .await?;

        Ok(permissions.into_iter().map(|row| row.get::<String, _>(0)).collect())
    }

    // User management methods
    pub async fn get_all_users(&self) -> Result<Vec<User>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let users = sqlx::query_as::<_, User>(
            "SELECT * FROM users ORDER BY created_at DESC"
        )
        .fetch_all(pool)
        .await?;

        Ok(users)
    }

    pub async fn create_user(&self, email: &str, full_name: &str, role: &str, password: &str) -> Result<i64> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let password_hash = bcrypt::hash(password, bcrypt::DEFAULT_COST)?;

        let result = sqlx::query(
            "INSERT INTO users (email, full_name, role, password_hash) VALUES (?, ?, ?, ?)"
        )
        .bind(email)
        .bind(full_name)
        .bind(role)
        .bind(password_hash)
        .execute(pool)
        .await?;

        Ok(result.last_insert_rowid())
    }

    pub async fn update_user(&self, user_id: i64, email: &str, full_name: &str, role: &str) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        sqlx::query(
            "UPDATE users SET email = ?, full_name = ?, role = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        )
        .bind(email)
        .bind(full_name)
        .bind(role)
        .bind(user_id)
        .execute(pool)
        .await?;

        Ok(())
    }

    pub async fn delete_user(&self, user_id: i64) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        sqlx::query("DELETE FROM users WHERE id = ?")
            .bind(user_id)
            .execute(pool)
            .await?;

        Ok(())
    }

    pub async fn get_all_roles(&self) -> Result<Vec<Role>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let roles = sqlx::query_as::<_, Role>(
            "SELECT * FROM roles ORDER BY name"
        )
        .fetch_all(pool)
        .await?;

        Ok(roles)
    }

    pub async fn get_all_permissions(&self) -> Result<Vec<Permission>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let permissions = sqlx::query_as::<_, Permission>(
            "SELECT * FROM permissions ORDER BY name"
        )
        .fetch_all(pool)
        .await?;

        Ok(permissions)
    }

    // Product management methods
    pub async fn get_all_products(&self) -> Result<Vec<Product>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let products = sqlx::query_as::<_, Product>(
            "SELECT * FROM products ORDER BY name"
        )
        .fetch_all(pool)
        .await?;

        Ok(products)
    }

    pub async fn create_product(&self, product: CreateProductRequest) -> Result<i64> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let result = sqlx::query(
            "INSERT INTO products (name, description, sku, category, price, cost, quantity, reorder_level, expiry_date, supplier_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
        )
        .bind(&product.name)
        .bind(&product.description)
        .bind(&product.sku)
        .bind(&product.category)
        .bind(product.price)
        .bind(product.cost)
        .bind(product.quantity)
        .bind(product.reorder_level)
        .bind(&product.expiry_date)
        .bind(product.supplier_id)
        .execute(pool)
        .await?;

        Ok(result.last_insert_rowid())
    }

    pub async fn update_product(&self, product_id: i64, product: CreateProductRequest) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        sqlx::query(
            "UPDATE products SET name = ?, description = ?, sku = ?, category = ?, price = ?, cost = ?, quantity = ?, reorder_level = ?, expiry_date = ?, supplier_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        )
        .bind(&product.name)
        .bind(&product.description)
        .bind(&product.sku)
        .bind(&product.category)
        .bind(product.price)
        .bind(product.cost)
        .bind(product.quantity)
        .bind(product.reorder_level)
        .bind(&product.expiry_date)
        .bind(product.supplier_id)
        .bind(product_id)
        .execute(pool)
        .await?;

        Ok(())
    }

    pub async fn delete_product(&self, product_id: i64) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        sqlx::query("DELETE FROM products WHERE id = ?")
            .bind(product_id)
            .execute(pool)
            .await?;

        Ok(())
    }

    pub async fn update_stock(&self, product_id: i64, stock_update: UpdateStockRequest) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        // Start a transaction
        let mut tx = pool.begin().await?;

        // Update product quantity
        sqlx::query(
            "UPDATE products SET quantity = quantity + ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        )
        .bind(stock_update.quantity_change)
        .bind(product_id)
        .execute(&mut *tx)
        .await?;

        // Record inventory movement
        sqlx::query(
            "INSERT INTO inventory_movements (product_id, quantity_change, movement_type, notes) VALUES (?, ?, ?, ?)"
        )
        .bind(product_id)
        .bind(stock_update.quantity_change)
        .bind(&stock_update.movement_type)
        .bind(&stock_update.notes)
        .execute(&mut *tx)
        .await?;

        tx.commit().await?;
        Ok(())
    }

    pub async fn get_low_stock_products(&self) -> Result<Vec<Product>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let products = sqlx::query_as::<_, Product>(
            "SELECT * FROM products WHERE quantity <= reorder_level ORDER BY quantity ASC"
        )
        .fetch_all(pool)
        .await?;

        Ok(products)
    }

    // Supplier management methods
    pub async fn get_all_suppliers(&self) -> Result<Vec<Supplier>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let suppliers = sqlx::query_as::<_, Supplier>(
            "SELECT * FROM suppliers ORDER BY name"
        )
        .fetch_all(pool)
        .await?;

        Ok(suppliers)
    }

    pub async fn create_supplier(&self, name: &str, contact_name: Option<&str>, email: Option<&str>, phone: Option<&str>, address: Option<&str>) -> Result<i64> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let result = sqlx::query(
            "INSERT INTO suppliers (name, contact_name, email, phone, address) VALUES (?, ?, ?, ?, ?)"
        )
        .bind(name)
        .bind(contact_name)
        .bind(email)
        .bind(phone)
        .bind(address)
        .execute(pool)
        .await?;

        Ok(result.last_insert_rowid())
    }

    pub async fn update_supplier(&self, supplier_id: i64, name: &str, contact_name: Option<&str>, email: Option<&str>, phone: Option<&str>, address: Option<&str>) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        sqlx::query(
            "UPDATE suppliers SET name = ?, contact_name = ?, email = ?, phone = ?, address = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        )
        .bind(name)
        .bind(contact_name)
        .bind(email)
        .bind(phone)
        .bind(address)
        .bind(supplier_id)
        .execute(pool)
        .await?;

        Ok(())
    }

    pub async fn delete_supplier(&self, supplier_id: i64) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        sqlx::query("DELETE FROM suppliers WHERE id = ?")
            .bind(supplier_id)
            .execute(pool)
            .await?;

        Ok(())
    }

    // Inventory movements
    pub async fn get_inventory_movements(&self, product_id: Option<i64>) -> Result<Vec<InventoryMovement>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let movements = if let Some(pid) = product_id {
            sqlx::query_as::<_, InventoryMovement>(
                "SELECT * FROM inventory_movements WHERE product_id = ? ORDER BY timestamp DESC"
            )
            .bind(pid)
            .fetch_all(pool)
            .await?
        } else {
            sqlx::query_as::<_, InventoryMovement>(
                "SELECT * FROM inventory_movements ORDER BY timestamp DESC LIMIT 100"
            )
            .fetch_all(pool)
            .await?
        };

        Ok(movements)
    }

    // POS-related methods
    pub async fn get_product_by_sku(&self, sku: &str) -> Result<Option<Product>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let product = sqlx::query_as::<_, Product>(
            "SELECT * FROM products WHERE sku = ?"
        )
        .bind(sku)
        .fetch_optional(pool)
        .await?;

        Ok(product)
    }

    pub async fn search_products_by_name(&self, query: &str) -> Result<Vec<Product>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let search_pattern = format!("%{}%", query);
        let products = sqlx::query_as::<_, Product>(
            "SELECT * FROM products WHERE name LIKE ? OR sku LIKE ? ORDER BY name LIMIT 20"
        )
        .bind(&search_pattern)
        .bind(&search_pattern)
        .fetch_all(pool)
        .await?;

        Ok(products)
    }

    pub async fn create_order(&self, order_data: CreateOrderRequest) -> Result<i64> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        // Start a transaction
        let mut tx = pool.begin().await?;

        // Calculate total amount
        let total_amount: f64 = order_data.items.iter()
            .map(|item| item.price_at_sale * item.quantity as f64)
            .sum();

        // Create order
        let order_result = sqlx::query(
            "INSERT INTO orders (customer_name, payment_method, total_amount, status) VALUES (?, ?, ?, 'pending')"
        )
        .bind(&order_data.customer_name)
        .bind(&order_data.payment_method)
        .bind(total_amount)
        .execute(&mut *tx)
        .await?;

        let order_id = order_result.last_insert_rowid();

        // Create order items and update stock
        for item in order_data.items {
            // Insert order item
            sqlx::query(
                "INSERT INTO order_items (order_id, product_id, quantity, price_at_sale) VALUES (?, ?, ?, ?)"
            )
            .bind(order_id)
            .bind(item.product_id)
            .bind(item.quantity)
            .bind(item.price_at_sale)
            .execute(&mut *tx)
            .await?;

            // Update product stock
            sqlx::query(
                "UPDATE products SET quantity = quantity - ? WHERE id = ?"
            )
            .bind(item.quantity)
            .bind(item.product_id)
            .execute(&mut *tx)
            .await?;

            // Record inventory movement
            sqlx::query(
                "INSERT INTO inventory_movements (product_id, quantity_change, movement_type, notes) VALUES (?, ?, 'sale', ?)"
            )
            .bind(item.product_id)
            .bind(-item.quantity)
            .bind(format!("Sale - Order #{}", order_id))
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;
        Ok(order_id)
    }

    pub async fn complete_order(&self, order_id: i64) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        sqlx::query(
            "UPDATE orders SET status = 'completed', updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        )
        .bind(order_id)
        .execute(pool)
        .await?;

        Ok(())
    }

    pub async fn cancel_order(&self, order_id: i64) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        // Start a transaction
        let mut tx = pool.begin().await?;

        // Get order items to restore stock
        let order_items = sqlx::query_as::<_, OrderItem>(
            "SELECT * FROM order_items WHERE order_id = ?"
        )
        .bind(order_id)
        .fetch_all(&mut *tx)
        .await?;

        // Restore stock for each item
        for item in order_items {
            sqlx::query(
                "UPDATE products SET quantity = quantity + ? WHERE id = ?"
            )
            .bind(item.quantity)
            .bind(item.product_id)
            .execute(&mut *tx)
            .await?;

            // Record inventory movement
            sqlx::query(
                "INSERT INTO inventory_movements (product_id, quantity_change, movement_type, notes) VALUES (?, ?, 'return', ?)"
            )
            .bind(item.product_id)
            .bind(item.quantity)
            .bind(format!("Order cancellation - Order #{}", order_id))
            .execute(&mut *tx)
            .await?;
        }

        // Update order status
        sqlx::query(
            "UPDATE orders SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        )
        .bind(order_id)
        .execute(&mut *tx)
        .await?;

        tx.commit().await?;
        Ok(())
    }

    pub async fn get_recent_orders(&self, limit: i64) -> Result<Vec<Order>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let orders = sqlx::query_as::<_, Order>(
            "SELECT * FROM orders ORDER BY created_at DESC LIMIT ?"
        )
        .bind(limit)
        .fetch_all(pool)
        .await?;

        Ok(orders)
    }

    pub async fn get_order_items(&self, order_id: i64) -> Result<Vec<OrderItem>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let items = sqlx::query_as::<_, OrderItem>(
            "SELECT * FROM order_items WHERE order_id = ?"
        )
        .bind(order_id)
        .fetch_all(pool)
        .await?;

        Ok(items)
    }

    // Notification management methods
    pub async fn create_notification(&self, user_id: Option<i64>, title: &str, message: &str, notification_type: &str, priority: &str, product_id: Option<i64>) -> Result<i64> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let result = sqlx::query(
            "INSERT INTO notifications (user_id, title, message, type, priority, product_id) VALUES (?, ?, ?, ?, ?, ?)"
        )
        .bind(user_id)
        .bind(title)
        .bind(message)
        .bind(notification_type)
        .bind(priority)
        .bind(product_id)
        .execute(pool)
        .await?;

        Ok(result.last_insert_rowid())
    }

    pub async fn get_notifications(&self, user_id: Option<i64>, unread_only: bool) -> Result<Vec<Notification>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let notifications = if let Some(uid) = user_id {
            if unread_only {
                sqlx::query_as::<_, Notification>(
                    "SELECT * FROM notifications WHERE (user_id = ? OR user_id IS NULL) AND is_read = FALSE ORDER BY created_at DESC"
                )
                .bind(uid)
                .fetch_all(pool)
                .await?
            } else {
                sqlx::query_as::<_, Notification>(
                    "SELECT * FROM notifications WHERE (user_id = ? OR user_id IS NULL) ORDER BY created_at DESC LIMIT 50"
                )
                .bind(uid)
                .fetch_all(pool)
                .await?
            }
        } else {
            if unread_only {
                sqlx::query_as::<_, Notification>(
                    "SELECT * FROM notifications WHERE is_read = FALSE ORDER BY created_at DESC"
                )
                .fetch_all(pool)
                .await?
            } else {
                sqlx::query_as::<_, Notification>(
                    "SELECT * FROM notifications ORDER BY created_at DESC LIMIT 50"
                )
                .fetch_all(pool)
                .await?
            }
        };

        Ok(notifications)
    }

    pub async fn mark_notification_read(&self, notification_id: i64) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        sqlx::query("UPDATE notifications SET is_read = TRUE WHERE id = ?")
            .bind(notification_id)
            .execute(pool)
            .await?;

        Ok(())
    }

    pub async fn get_expiring_products(&self, days_ahead: i32) -> Result<Vec<Product>> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        let products = sqlx::query_as::<_, Product>(
            "SELECT * FROM products WHERE expiry_date IS NOT NULL AND expiry_date <= date('now', '+' || ? || ' days') ORDER BY expiry_date ASC"
        )
        .bind(days_ahead)
        .fetch_all(pool)
        .await?;

        Ok(products)
    }

    pub async fn check_and_create_alerts(&self) -> Result<()> {
        let pool = self.pool.as_ref().ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        // Check for low stock products
        let low_stock_products = self.get_low_stock_products().await?;
        for product in low_stock_products {
            // Check if we already have a recent notification for this product
            let existing = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM notifications WHERE product_id = ? AND type = 'low_stock' AND created_at > datetime('now', '-1 day')"
            )
            .bind(product.id)
            .fetch_one(pool)
            .await?;

            if existing == 0 {
                self.create_notification(
                    None, // Send to all managers/admins
                    "Low Stock Alert",
                    &format!("Product '{}' is running low. Current stock: {}, Reorder level: {}",
                            product.name, product.quantity, product.reorder_level),
                    "low_stock",
                    "high",
                    Some(product.id)
                ).await?;
            }
        }

        // Check for expiring products (within 7 days)
        let expiring_products = self.get_expiring_products(7).await?;
        for product in expiring_products {
            // Check if we already have a recent notification for this product
            let existing = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM notifications WHERE product_id = ? AND type = 'expiry_warning' AND created_at > datetime('now', '-1 day')"
            )
            .bind(product.id)
            .fetch_one(pool)
            .await?;

            if existing == 0 {
                let days_until_expiry = if let Some(expiry_date) = &product.expiry_date {
                    // Calculate days until expiry
                    "soon"
                } else {
                    "unknown"
                };

                self.create_notification(
                    None, // Send to all managers/admins
                    "Product Expiry Warning",
                    &format!("Product '{}' expires {}. Expiry date: {}",
                            product.name, days_until_expiry,
                            product.expiry_date.as_deref().unwrap_or("Unknown")),
                    "expiry_warning",
                    "medium",
                    Some(product.id)
                ).await?;
            }
        }

        Ok(())
    }
}
