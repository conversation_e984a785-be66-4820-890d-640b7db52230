use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct User {
    pub id: i64,
    pub email: String,
    pub full_name: String,
    pub role: String,
    pub password_hash: String,
    pub last_login: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct Role {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Permission {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::FromRow)]
pub struct RolePermission {
    pub role_id: i64,
    pub permission_id: i64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct Product {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub sku: String,
    pub category: String,
    pub price: f64,
    pub cost: f64,
    pub quantity: i32,
    pub reorder_level: i32,
    pub supplier_id: Option<i64>,
    pub last_synced_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Supplier {
    pub id: i64,
    pub name: String,
    pub contact_name: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub address: Option<String>,
    pub last_synced_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Order {
    pub id: i64,
    pub customer_name: Option<String>,
    pub payment_method: String,
    pub total_amount: f64,
    pub status: String,
    pub order_date: DateTime<Utc>,
    pub last_synced_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct OrderItem {
    pub id: i64,
    pub order_id: i64,
    pub product_id: i64,
    pub quantity: i32,
    pub price_at_sale: f64,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct InventoryMovement {
    pub id: i64,
    pub product_id: i64,
    pub quantity_change: i32,
    pub movement_type: String, // 'sale', 'purchase', 'adjustment', 'return'
    pub notes: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub last_synced_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
}

// DTOs for API requests/responses
#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    pub email: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub user: UserInfo,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: i64,
    pub email: String,
    pub full_name: String,
    pub role: String,
    pub permissions: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProductRequest {
    pub name: String,
    pub description: Option<String>,
    pub sku: String,
    pub category: String,
    pub price: f64,
    pub cost: f64,
    pub quantity: i32,
    pub reorder_level: i32,
    pub supplier_id: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateStockRequest {
    pub quantity_change: i32,
    pub movement_type: String,
    pub notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateOrderRequest {
    pub customer_name: Option<String>,
    pub payment_method: String,
    pub items: Vec<OrderItemRequest>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrderItemRequest {
    pub product_id: i64,
    pub quantity: i32,
    pub price_at_sale: f64,
}
